{"name": "charity-donation-frontend", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@cloudinary/react": "^1.13.0", "@cloudinary/url-gen": "^1.20.0", "@date-io/dayjs": "^3.2.0", "@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@hookform/resolvers": "^5.0.1", "@mui/icons-material": "^7.1.0", "@mui/material": "^7.1.0", "@mui/x-date-pickers": "^8.3.1", "@reduxjs/toolkit": "^2.8.1", "@stripe/react-stripe-js": "^3.7.0", "@stripe/stripe-js": "^4.10.0", "@tailwindcss/forms": "^0.5.10", "@tailwindcss/typography": "^0.5.16", "axios": "^1.9.0", "bcryptjs": "^3.0.2", "chart.js": "^4.4.9", "date-fns": "^4.1.0", "dayjs": "^1.11.13", "dotenv": "^16.5.0", "firebase": "^11.7.1", "formik": "^2.4.6", "framer-motion": "^12.10.5", "js-cookie": "^3.0.5", "jsonwebtoken": "^9.0.2", "lightningcss": "^1.30.1", "lucide-react": "^0.510.0", "next": "^15.3.1", "node-fetch": "^3.3.2", "react": "^18.3.1", "react-chartjs-2": "^5.3.0", "react-dom": "^18.3.1", "react-hook-form": "^7.56.3", "react-hot-toast": "^2.5.2", "react-icons": "^5.5.0", "react-redux": "^9.2.0", "react-toastify": "^11.0.5", "recharts": "^2.15.3", "socket.io-client": "^4.8.1", "tailwind-merge": "^3.3.0", "yup": "^1.6.1"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4.1.6", "@types/bcryptjs": "^2.4.6", "@types/js-cookie": "^3.0.6", "@types/jsonwebtoken": "^9.0.9", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "@types/react-redux": "^7.1.34", "autoprefixer": "^10.4.21", "eslint": "^9", "eslint-config-next": "15.3.1", "postcss": "^8.5.3", "tailwindcss": "^4.1.6", "typescript": "^5"}}