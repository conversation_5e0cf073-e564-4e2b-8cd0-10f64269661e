import { DonationType } from "./donation";

export interface ItemGoal {
	itemType: string;
	targetQuantity: number;
	receivedQuantity: number;
	unit: string;
}

export interface Cause {
	id: string;
	title: string;
	description: string;
	targetAmount: number;
	raisedAmount: number;
	imageUrl: string;
	tags: string[];
	organizationId: string;
	organizationName?: string;
	organizationUserId?: string; // Add this for messaging
	createdAt: string;
	updatedAt: string;
	donorCount?: number;
	acceptedDonationTypes?: DonationType[];
	acceptanceType?: "money" | "items" | "both";
	donationItems?: string[];
	targetDescription?: string;
	itemGoals?: ItemGoal[];
}

export interface CreateCauseBody {
	title: string;
	description: string;
	targetAmount: number;
	imageUrl: string;
	tags: string[];
	organizationId: string;
	acceptanceType: "money" | "items" | "both";
	donationItems: string[];
	targetDescription?: string;
	itemGoals?: ItemGoal[];
}

export interface UpdateCauseBody {
	title?: string;
	description?: string;
	targetAmount?: number;
	imageUrl?: string;
	tags?: string[];
	acceptanceType?: "money" | "items" | "both";
	donationItems?: string[];
	targetDescription?: string;
	itemGoals?: ItemGoal[];
}

export interface CausesResponse {
	causes: Cause[];
	total: number;
	page: number;
	limit: number;
	totalPages: number;
}

export interface CauseQueryParams {
	page?: number;
	limit?: number;
	search?: string;
	tags?: string[];
	organizationId?: string;
	minTarget?: number;
	maxTarget?: number;
	minRaised?: number;
	maxRaised?: number;
	donationType?: DonationType;
}

export interface CauseResponse {
	cause: {
		id: string;
		title: string;
		description: string;
		targetAmount: number;
		raisedAmount: number;
		imageUrl: string;
		tags: string[];
		organizationId: string;
		organizationName?: string;
		organizationUserId?: string; // Add this for messaging
		createdAt: string;
		updatedAt: string;
		donorCount?: number;
		acceptedDonationTypes?: DonationType[];
		acceptanceType?: "money" | "items" | "both";
		donationItems?: string[];
		targetDescription?: string;
		itemGoals?: ItemGoal[];
		endDate: Date;
	};
}
