// Item Goal interface
export interface ItemGoal {
	itemType: string;
	targetQuantity: number;
	receivedQuantity: number;
	unit: string;
}

// Cause types
export interface Cause {
	id: string;
	title: string;
	description: string;
	targetAmount: number;
	raisedAmount: number;
	imageUrl: string;
	tags: string[];
	category?: string;
	status?: "active" | "completed" | "draft";
	organizationId: string;
	organizationName?: string;
	acceptanceType?: "money" | "items" | "both";
	donationItems?: string[];
	targetDescription?: string;
	itemGoals?: ItemGoal[];
	createdAt: string;
	updatedAt: string;
}

export interface CauseResponse {
	success: boolean;
	cause: Cause;
}

export interface CausesResponse {
	success: boolean;
	causes: Cause[];
	total: number;
	page: number;
	limit: number;
}

export interface CauseQueryParams {
	page?: number;
	limit?: number;
	search?: string;
	tag?: string;
	sort?: string;
}

export interface CreateCauseBody {
	title: string;
	description: string;
	targetAmount: number;
	imageUrl: string;
	tags?: string[];
	category?: string;
	status?: "active" | "completed" | "draft";
	acceptanceType?: "money" | "items" | "both";
	donationItems?: string[];
	targetDescription?: string;
	itemGoals?: ItemGoal[];
}

export interface UpdateCauseBody {
	title?: string;
	description?: string;
	targetAmount?: number;
	imageUrl?: string;
	tags?: string[];
	category?: string;
	status?: "active" | "completed" | "draft";
	acceptanceType?: "money" | "items" | "both";
	donationItems?: string[];
	targetDescription?: string;
	itemGoals?: ItemGoal[];
}
