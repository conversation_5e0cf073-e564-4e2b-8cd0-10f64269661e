import mongoose from 'mongoose';
import Cause from '../models/cause.model';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

const migrateItemGoals = async () => {
  try {
    // Connect to MongoDB
    const mongoUri = process.env.MONGODB_URI || 'mongodb://localhost:27017/charity_donation';
    await mongoose.connect(mongoUri);
    console.log('Connected to MongoDB');

    // Find all causes that don't have itemGoals field or have null/undefined itemGoals
    const causesWithoutItemGoals = await Cause.find({
      $or: [
        { itemGoals: { $exists: false } },
        { itemGoals: null },
        { itemGoals: undefined }
      ]
    });

    console.log(`Found ${causesWithoutItemGoals.length} causes without itemGoals field`);

    if (causesWithoutItemGoals.length === 0) {
      console.log('All causes already have itemGoals field. Migration not needed.');
      return;
    }

    // Update each cause to add empty itemGoals array
    const updatePromises = causesWithoutItemGoals.map(async (cause) => {
      return Cause.findByIdAndUpdate(
        cause._id,
        { 
          $set: { 
            itemGoals: [],
            // Also ensure targetDescription exists
            ...(cause.targetDescription === undefined && { targetDescription: "" })
          } 
        },
        { new: true }
      );
    });

    await Promise.all(updatePromises);

    console.log(`Successfully migrated ${causesWithoutItemGoals.length} causes`);
    console.log('Migration completed successfully!');

  } catch (error) {
    console.error('Migration failed:', error);
  } finally {
    // Close the database connection
    await mongoose.connection.close();
    console.log('Database connection closed');
  }
};

// Run the migration
if (require.main === module) {
  migrateItemGoals()
    .then(() => {
      console.log('Migration script finished');
      process.exit(0);
    })
    .catch((error) => {
      console.error('Migration script failed:', error);
      process.exit(1);
    });
}

export default migrateItemGoals;
